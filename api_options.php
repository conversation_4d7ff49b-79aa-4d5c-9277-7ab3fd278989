<?php
header("Access-Control-Allow-Origin: *");
header("Content-Type: application/json; charset=UTF-8");
header("Access-Control-Allow-Methods: GET");
header("Access-Control-Max-Age: 3600");
header("Access-Control-Allow-Headers: Content-Type, Access-Control-Allow-Headers, Authorization, X-Requested-With");

require_once("api_function.php");

$Flag = $_GET["f"] ?? "";
if ($Flag === "") {
    $Result = ListOptions();
} else if (in_array($Flag, ["provincial", "station"])) {
    $FId = ($Flag === "provincial") ? "regionId" : "provincialId";
    $DataId = GetParam($FId);

    if ($DataId != "") {
        $InputVals = [$DataId, $Flag];
        $Result = ListPoliceStationTree($InputVals);

        if (empty($Result)) {
            $Result = ["code" => "LIST_OPTIONS_FAILED", 
                       "message" => $Flag === "provincial" 
                                    ? "Police provincial not found" 
                                    : "Police station not found",
                       "status" => false
            ];
        }
    } else {
        $Result = ["code" => "LIST_OPTIONS_FAILED", 
                   "message" => "Invalid parameters",
                   "status" => false
        ];
    }
} else {
    $Result = ["code" => "LIST_OPTIONS_FAILED", 
               "message" => "Invalid parameters",
               "status" => false
    ];
}

http_response_code(200);
echo json_encode($Result);
return;
?>