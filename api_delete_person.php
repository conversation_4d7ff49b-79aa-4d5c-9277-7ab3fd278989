<?php
header("Access-Control-Allow-Origin: *");
header("Content-Type: application/json; charset=UTF-8");
header("Access-Control-Allow-Methods: DELETE");
header("Access-Control-Max-Age: 3600");
header("Access-Control-Allow-Headers: Content-Type, Access-Control-Allow-Headers, Authorization, X-Requested-With");

require_once("api_function.php");

$IdCard = (isset($_GET['idCard']) && trim($_GET['idCard'])) ? trim($_GET['idCard']) : '';
if ($IdCard != "") {
    $ChkId = [$IdCard, "idcard"];
    $CheckPerson = GetPersonDetails($ChkId);
    if ($CheckPerson["status"] === true) {
        $OldImg = "";
        if (!empty($CheckPerson["details"]["imagePath"])) {
            $Fullpath = parse_url($CheckPerson["details"]["imagePath"], PHP_URL_PATH);
            $OldImg = ltrim($Fullpath, '/');
        }

        $Values = [json_encode(["idcard" => $IdCard]), "delete"];
        $Result = PersonManagement($Values);

        if ($Result["status"] === true) {
            if ($OldImg !="") {
                unlink("../".$OldImg);
            }
        }
    } else {
        $Result = ["code" => "DELETE_PERSON_FAILED",
                   "message" => "Person not found",
                   "status" => false];
    }
} else {
    $Result = ["code" => "DELETE_PERSON_FAILED",
               "message" => "Invalid paramters",
               "status" => false];
}

http_response_code(200);
echo json_encode($Result);
return;
?>