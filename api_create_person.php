<?php
header("Access-Control-Allow-Origin: *");
header("Content-Type: application/json; charset=UTF-8");
header("Access-Control-Allow-Methods: POST, PUT");
header("Access-Control-Max-Age: 3600");
header("Access-Control-Allow-Headers: Content-Type, Access-Control-Allow-Headers, Authorization, X-Requested-With");

require_once("api_function.php");

$ListOptions = ListOptions()["listOptions"];
$Type = (isset($_POST['type']) && FindListOptions($ListOptions['type'], intval(trim($_POST['type'])))) ? intval(trim($_POST['type'])) : '';
$PersonalType = (isset($_POST['personalType']) && FindListOptions($ListOptions['personType'], intval(trim($_POST['personalType'])))) ? intval(trim($_POST['personalType'])) : '';
$CrimesType = (isset($_POST['crimesType']) && FindListOptions($ListOptions['crimesType'], intval(trim($_POST['crimesType'])))) ? intval(trim($_POST['crimesType'])) : '';
// รับข้อมูลหมายเลขประจำตัว (idcard) - รองรับหลายรูปแบบ
$IdCard = (isset($_POST['idcard']) && trim($_POST['idcard']) != '') ? trim($_POST['idcard']) : '';
$Passport = (isset($_POST['passport']) && trim($_POST['passport']) != '') ? trim($_POST['passport']) : NULL;
$Titlename = (isset($_POST['titlename']) && FindListOptions($ListOptions['titlename'], trim($_POST['titlename']))) ? trim($_POST['titlename']) : '';
$SubTitlename = (isset($_POST['subTitlename']) && trim($_POST['subTitlename']) != '') ? trim($_POST['subTitlename']) : '';
$Firstname = (isset($_POST['firstname']) && trim($_POST['firstname']) != '') ? trim($_POST['firstname']) : '';
$Lastname = (isset($_POST['lastname']) && trim($_POST['lastname']) != '') ? trim($_POST['lastname']) : '';
$Nickname = (isset($_POST['nickname']) && trim($_POST['nickname']) != '') ? trim($_POST['nickname']) : NULL;
$Sex = (isset($_POST['sex']) && FindListOptions($ListOptions['sex'], trim($_POST['sex']))) ? trim($_POST['sex']) : '';
$Birthday = (isset($_POST['birthday']) && trim($_POST['birthday']) != '') ? trim($_POST['birthday']) : '';
$Age = (isset($_POST['age']) && is_numeric(trim($_POST['age'])) != '') ? intval(trim($_POST['age'])) : '';
$FatherName = (isset($_POST['fatherName']) && trim($_POST['fatherName']) != '') ? trim($_POST['fatherName']) : NULL;
$FatherIdcard = (isset($_POST['fatherIdcard']) && trim($_POST['fatherIdcard']) != '') ? trim($_POST['fatherIdcard']) : NULL;
$MotherName = (isset($_POST['motherName']) && trim($_POST['motherName']) != '') ? trim($_POST['motherName']) : NULL;
$MotherIdcard = (isset($_POST['motherIdcard']) && trim($_POST['motherIdcard']) != '') ? trim($_POST['motherIdcard']) : NULL;
$MaritalStatus = (isset($_POST['maritalStatus']) && trim($_POST['maritalStatus']) != '') ? trim($_POST['maritalStatus']) : '';
$DeathStatus = (isset($_POST['deathStatus']) && FindListOptions($ListOptions['deathStatus'], trim($_POST['deathStatus']))) ? intval(trim($_POST['deathStatus'])) : '';
$DateOfDeath = (isset($_POST['dateOfDeath']) && trim($_POST['dateOfDeath']) != '') ? trim($_POST['dateOfDeath']) : '';
$PoliceRegion = (isset($_POST['policeRegion']) && FindListOptions($ListOptions['policeRegion'], trim($_POST['policeRegion']))) ? trim($_POST['policeRegion']) : '';
$PoliceProvincial = (isset($_POST['policeProvincial']) && trim($_POST['policeProvincial']) != '') ? trim($_POST['policeProvincial']) : '';
$PoliceStation = (isset($_POST['policeStation']) && trim($_POST['policeStation']) != '') ? trim($_POST['policeStation']) : '';
$UserRecorder = (isset($_POST['userRecorder']) && trim($_POST['userRecorder']) != '') ? trim($_POST['userRecorder']) : '';
$UserPosition = (isset($_POST['userPosition']) && trim($_POST['userPosition']) != '') ? trim($_POST['userPosition']) : '';
$Photo = (isset($_FILES['photo']) && $_FILES['photo']['error'] === UPLOAD_ERR_OK) ? $_FILES['photo']["tmp_name"] : "";

if ($Type == 1 || $Type == 2) {
    if ($Type == 1) {
        $CrimesType = NULL;
    } else {
        $PersonalType = NULL;
    }
}

// ตรวจสอบและจัดการหมายเลขประจำตัว
$ValidIdCard = "";
$ValidPassport = NULL;

if ($IdCard != "") {
    if (ValidateIdentificationNumber($IdCard)) {
        $idType = GetIdentificationNumberType($IdCard);
        if ($idType === 'thai_id') {
            $ValidIdCard = $IdCard;
        } else {
            // หากเป็นหมายเลขทะเบียนหรือพาสปอร์ต ให้เก็บใน passport field
            $ValidPassport = $IdCard;
            $ValidIdCard = ""; // ไม่เก็บใน idcard field
        }
    }
}

// หากมีการส่ง passport มาแยกต่างหาก
if ($Passport != NULL && ValidateIdentificationNumber($Passport)) {
    $ValidPassport = $Passport;
}

// ตรวจสอบ idcard ของบิดา-มารดา (ต้องเป็นเลขบัตรประชาชนไทยเท่านั้น)
$FatherIds = ($FatherIdcard != NULL && ValidateThaiID($FatherIdcard)) ? $FatherIdcard : NULL;
$MotherIds = ($MotherIdcard != NULL && ValidateThaiID($MotherIdcard)) ? $MotherIdcard : NULL;

$TName = "";
if ($Titlename !="") {
    $TName = ($Titlename === "อื่นๆ") ? $SubTitlename : $Titlename;
}
if ($Birthday != "") {
    $BrithdayTH = formatThaiDate($Birthday, "iso");
    $Age = NULL;
} else {
    $Birthday = $BrithdayTH = NULL;
}

if ($DeathStatus == 2) {
    $DateOfDeath = (checkDateFormat($DateOfDeath, "iso")) ? $DateOfDeath : "";
} else {
    $DateOfDeath = NULL;
}

$CheckPoliceStation = $CheckProvincial = $CheckStation = false;
if ($PoliceRegion !='' && $PoliceProvincial !='' && $PoliceStation !='') {
    $Provincial = [$PoliceRegion, $PoliceProvincial, 'provincial'];
    $CheckProvincial = CheckPoliceStation($Provincial);

    $Station = [$PoliceProvincial, $PoliceStation, 'station'];
    $CheckStation = CheckPoliceStation($Station);

    if (!$CheckProvincial || !$CheckStation) {
        $PoliceRegion = $PoliceProvincial = $PoliceStation = '';
    }
} else {
    $PoliceRegion = $PoliceProvincial = $PoliceStation = '';
}

$InputVals = ["id" => NULL, "type" => $Type, "personType" => $PersonalType, "crimesType" => $CrimesType,
              "idcard" => $ValidIdCard, "passport" => $ValidPassport, "titlename" => $TName, "firstname" => $Firstname,
              "lastname" => $Lastname, "nickname" => $Nickname, "sex" => $Sex, "birthdayTH" => $BrithdayTH,
              "birthdayEN" => $Birthday, "age" => $Age, "fatherName" => $FatherName, "fatherIdcard" => $FatherIds,
              "motherName" => $MotherName, "motherIdcard" => $MotherIds, "maritalStatus" => $MaritalStatus,
              "deathStatus" => $DeathStatus, "deathDate" => $DateOfDeath, "policeRegion" => $PoliceRegion,
              "policeProvincial" => $PoliceProvincial, "policeStation" => $PoliceStation,
              "userRecorder" => $UserRecorder, "userPosition" => $UserPosition,
              "recordDate" => date('Y-m-d H:i:s'), "imagePath" => NULL];

// ตรวจสอบว่ามีหมายเลขประจำตัวอย่างน้อย 1 อย่าง
if (empty($ValidIdCard) && empty($ValidPassport)) {
    $Result = ["code" => "ADD_PERSON_FAILED",
               "message" => "กรุณาระบุหมายเลขประจำตัว (เลขบัตรประชาชน, หมายเลขทะเบียน, หรือหมายเลขพาสปอร์ต)",
               "status" => false];
} else {
    $isValid = ValidationData($InputVals);
    if ($isValid) {
    // Upload image
    if (!empty($Photo)) {
        // ใช้หมายเลขประจำตัวที่มีอยู่สำหรับตั้งชื่อไฟล์
        $identificationNumber = !empty($ValidIdCard) ? $ValidIdCard : $ValidPassport;
        $ImgPath = "/uploaded/Image_PS/ID_".$identificationNumber."_".date("Y-m-d_H:i:s").".jpg";
        $tmp = file_get_contents($Photo);
        $SaveTo = file_put_contents("..".$ImgPath, $tmp);

        $InputVals["imagePath"] = $ImgPath;
    }

    $Values = [json_encode($InputVals), "create"];
    $Result = PersonManagement($Values);
    if ($Result["status"] === false) {
        unlink("..".$ImgPath);
    }
} else {
    $Result = ["code" => "ADD_PERSON_FAILED",
               "message" => "Invalid paramters",
               "status" => false];
}
}

http_response_code(200);
echo json_encode($Result);
return;
?>