<?php
require_once("../Condb.php");

function GetParam($key) {
    return isset($_GET[$key]) && trim($_GET[$key]) !== "" ? trim($_GET[$key]) : "";
}

function NormalizeAndSort($data) {
    $converted = array_map(function($item) {
        $item['id'] = (int)$item['id'];
        return $item;
    }, $data);
    
    usort($converted, function($a, $b) {
        return $a['id'] <=> $b['id'];
    });
    
    return $converted;
}

function FindListOptions($Options, $id) {
    $Result = '';

    foreach ($Options as $value) {
        $Gettype = gettype($value);
        if ($Gettype == 'array') {
            if ($value["id"] == $id) {
                $Result = true;
                break;
            }
        } else {
            if ($value->id == $id) {
                $Result = true;
                break;
            }
        }
    }

    return $Result;
}

function CheckPoliceStation($Values) {
    global $conn;

    $sql = "CALL Check_PoliceStation(?, ?, ?, @result)";
    $query = mysqli_prepare($conn, $sql);
    mysqli_stmt_bind_param($query, "sss", ...$Values);
    mysqli_stmt_execute($query);
    mysqli_stmt_close($query);

    $result = mysqli_query($conn, "SELECT @result AS result");
    $res = mysqli_fetch_assoc($result);

    return boolval($res["result"]);
}

function checkDateFormat($dateStr, $formatKey) {
    $formats = ['iso' => 'Y-m-d', 'eng' => 'd/m/Y'];
    if (!isset($formats[$formatKey])) {
        return false;
    }

    $format = $formats[$formatKey];
    $dt = DateTime::createFromFormat($format, $dateStr);
    
    return ($dt && $dt->format($format) === $dateStr);
}

function formatThaiDate($dateStr, $format) {
    $Result = "";
    
    $CheckFormat = checkDateFormat($dateStr, $format);
    if ($CheckFormat) {
        $months = [
            1 => "มกราคม", 2 => "กุมภาพันธ์", 3 => "มีนาคม", 4 => "เมษายน",
            5 => "พฤษภาคม", 6 => "มิถุนายน", 7 => "กรกฎาคม", 8 => "สิงหาคม",
            9 => "กันยายน", 10 => "ตุลาคม", 11 => "พฤศจิกายน", 12 => "ธันวาคม"
        ];

        $date = new DateTime($dateStr);
        $day = $date->format("j");
        $month = $months[(int)$date->format("n")];
        $year = (int)$date->format("Y") + 543;

        $Result = "{$day} {$month} {$year}";    
    }

    return $Result;
}

function ValidateThaiID($ids) {
    $sum = 0;
    for ($i = 0; $i < 12; $i++) {
        $sum += (int)$ids[$i] * (13 - $i);
    }
    
    $checkDigit = (11 - ($sum % 11)) % 10;
    $Result = ($checkDigit == $ids[12]) ?? false;

    return $Result;
}

function ValidationData($Data) {
    // กำหนดฟิลด์ที่จำเป็นต้องมีค่า (ไม่สามารถเป็นค่าว่างได้)
    $requiredFields = [
        'type', 'idcard', 'titlename', 'firstname', 'lastname',
        'sex', 'deathStatus', 'userRecorder', 'userPosition'
    ];

    // กำหนดฟิลด์ที่ต้องมีค่าตามเงื่อนไข
    $conditionalFields = [
        'personType' => ['type' => 1],  // ต้องมีค่าเมื่อ type = 1
        'crimesType' => ['type' => 2],  // ต้องมีค่าเมื่อ type = 2
        'deathDate' => ['deathStatus' => 2]  // ต้องมีค่าเมื่อ deathStatus = 2
    ];

    foreach ($Data as $key => $val) {
        if (is_array($val)) {
            if (!ValidationData($val)) return false;
        } else {
            // ตรวจสอบฟิลด์ที่จำเป็น
            if (in_array($key, $requiredFields) && ($val === "" || $val === null)) {
                return false;
            }

            // ตรวจสอบฟิลด์ที่ต้องมีค่าตามเงื่อนไข
            foreach ($conditionalFields as $field => $condition) {
                if ($key === $field) {
                    $conditionKey = array_keys($condition)[0];
                    $conditionValue = $condition[$conditionKey];

                    if (isset($Data[$conditionKey]) && $Data[$conditionKey] == $conditionValue) {
                        if ($val === "" || $val === null) {
                            return false;
                        }
                    }
                }
            }

            // ตรวจสอบเลขบัตรประชาชน (ถ้ามี)
            if ($key === 'idcard' && $val !== "" && !ValidateThaiID($val)) {
                return false;
            }
        }
    }
    return true;
}

function ListOptions() {
    global $conn;

    $sql = "CALL Dropdown_ListOptions(@result)";
    $query = mysqli_prepare($conn, $sql);
    mysqli_stmt_execute($query);
    mysqli_stmt_close($query);
    
    $flag_result = mysqli_query($conn, "SELECT @result AS result");
    $res = mysqli_fetch_assoc($flag_result);
    $Decode = json_decode($res["result"],true);
    
    $PersonType = json_decode($Decode['personType']);
    $CrimesType = json_decode($Decode['crimesType']);
    $PoliceRegion = json_decode($Decode['policeRegion']);
    $DePoliceRegion = json_decode(json_encode($PoliceRegion), true);
    $SortPoliceRegion = NormalizeAndSort($DePoliceRegion);

    $Type = [["id" => 1, "title" => "บุคคลทั่วไป"], ["id" => 2, "title" => "บุคคลเกี่ยวข้องอาชญากรรม"]];
    $DeathStatus = [["id" => 1, "title" => "ยังมีชีวิต"], ["id" => 2, "title" => "เสียชีวิต"]];

    $Titlename = [["id" => "นาย", "title" => "นาย"], ["id" => "นาง", "title" => "นาง"],
                  ["id" => "น.ส.", "title" => "น.ส."], ["id" => "ด.ช.", "title" => "ด.ช."],
                  ["id" => "ด.ญ.", "title" => "ด.ญ."], ["id" => "อื่นๆ", "title" => "อื่นๆ"]];

    $SexStatus = [["id" => "ชาย", "title" => "ชาย"], ["id" => "หญิง", "title" => "หญิง"],
                  ["id" => "LGBTQ", "title" => "LGBTQ"]];

    $MaritalStatus = [["id" => "โสด", "title" => "โสด"], ["id" => "สมรส", "title" => "สมรส"],
                      ["id" => "หย่า", "title" => "หย่า"], ["id" => "หม้าย", "title" => "หม้าย"],
                      ["id" => "อื่นๆ", "title" => "อื่นๆ"]];
    $Notate = [["id" => "บุคคลตามหมายจับ", "title" => "บุคคลตามหมายจับ"], 
               ["id" => "บุคคลเฝ้าระวัง", "title" => "บุคคลเฝ้าระวัง"]];
                 
    $Result["listOptions"] = ["type" => $Type,"notate"=> $Notate, "personType" => $PersonType, "crimesType" => $CrimesType, 
                              "titlename" => $Titlename, "maritalStatus"=> $MaritalStatus, "sex" => $SexStatus, 
                              "deathStatus" => $DeathStatus, "policeRegion" => $SortPoliceRegion];
    
    return $Result;
}

function ListPoliceStationTree($Values) {
    global $conn;

    $Result = [];
    
    $sql = "CALL Dropdown_ListPoliceStation(?, ?, @result, @flag_err)";
    $query = mysqli_prepare($conn, $sql);
    mysqli_stmt_bind_param($query, "ss", ...$Values);
    mysqli_stmt_execute($query);
    mysqli_stmt_close($query);
    
    $result = mysqli_query($conn, "SELECT @result AS result, @flag_err AS flag_err");
    $res = mysqli_fetch_assoc($result);

    if ($res["flag_err"] == 0) {
        $Details = json_decode($res['result'], true);
        $ListBy = ($Values[1] == "provincial") ? "listPoliceProvincial" : "listPoliceStation";
        $Decode = json_decode($Details[$ListBy], true);
        $List = NormalizeAndSort($Decode);
        
        $Result[$ListBy] = $List;
    }

    return $Result;
}

function GetPersonDetails($Values) {
    global $conn;

    $sql = "CALL Person_GetDetails (?, ?, @result, @flag_err)";
    $query = mysqli_prepare($conn, $sql);
    mysqli_stmt_bind_param($query, "ss", ...$Values);
    mysqli_stmt_execute($query);
    mysqli_stmt_free_result( $query);
    mysqli_stmt_close($query);

    $result = mysqli_query($conn, "SELECT @result AS result, @flag_err AS flag_err");
    $res = mysqli_fetch_assoc($result);

    if ($res["flag_err"] == 0) {
        $jsonOutput = $res['result'] ?? '';
        $data = json_decode($jsonOutput, true);
        $Details = $data["personDetails"];
        $Img = $Details["imagePath"];
        
        if (!empty($Img)) {
            $Replace = str_replace("..", "", $Img);
            $Details["imagePath"] = "https://".$_SERVER["HTTP_HOST"].$Replace;
        }

        foreach ($Details as $key => $val) {
            if (is_null($val)) {
                $Details[$key] = "";
            }
        }

        $Result = array("details" => $Details, "status" => true);
    } else {
        $Result = array("code" => "GET_PERSON_DETAILS_FAILED", 
                        "message" => "Person not found", 
                        "status" => false);
    }

    return $Result;
}

function ListPersons($Search, $Page, $PerPages) {
    global $conn;

    $List = [];
    $TotalPages = $TotalRecs = 0;
    $SortField = 'ps_cl_date_rec';
    $SortOrder = 'DESC';

    $vals = [$Search, $Page, $PerPages, $SortField, $SortOrder];
    $sql = "CALL Person_List (?, ?, ?, ?, ?, @return_total_page, @return_total_recs)";
    $query = mysqli_prepare($conn, $sql);
    mysqli_stmt_bind_param($query, "siiss", ...$vals);
    mysqli_stmt_execute($query);
    $res = mysqli_stmt_get_result($query);

    if ($res !== false && $res->num_rows > 0) {
        while ($rows = mysqli_fetch_assoc($res)) {
            $ImgPath = "";
            if (!empty($rows["ps_cl_image"])) {
                $Replace = str_replace("..", "", $rows["ps_cl_image"]);
                $ImgPath = "https://".$_SERVER["HTTP_HOST"].$Replace;
            }

            if (!empty($rows["birthdayEN"])) {
                $birthDate = new DateTime($rows["birthdayEN"]);
                $today = new DateTime();
                $Age = $birthDate->diff($today)->y;
            } else {
                $Age = $rows["age"];
            }
            
            $Details = array("id" => $rows["ps_cl_aid"], "type" => $rows["type"], "personType" => $rows["person_type"],
                             "crimesType" => $rows["crimes_type"], "idcard" => $rows["ps_cl_idcard"],
                             "passport" => $rows["ps_cl_passport"], "fullname" => $rows["name"], 
                             "nickname" => $rows["ps_cl_nickname"], "sex" => $rows["ps_cl_sex"], 
                             "birthday" => $rows["birthday"], "age" => $Age.' ปี',
                             "deathStatus" => $rows["death_status"], "dateOfDeath" => $rows["date_of_death"],
                             "imagePath" => $ImgPath, "recordDate" => $rows["ps_cl_date_rec"]);

            foreach ($Details as $key => $val) {
                if (is_null($val)) {
                    $Details[$key] = "";
                }
            }

            array_push($List, $Details);
        }

        mysqli_stmt_free_result($query);
        mysqli_next_result($conn);
        $res_total = mysqli_query($conn, "SELECT @return_total_page AS return_total_page, 
                                                 @return_total_recs AS return_total_recs");
        $total = mysqli_fetch_assoc($res_total);
        $TotalPages = $total["return_total_page"];
        $TotalRecs = $total["return_total_recs"];
    }

    $Pagination = array("page" => $Page, "perPages" => $PerPages, 
                        "totalPages" => intval($TotalPages), "totalRecords" => intval($TotalRecs),
                        "sortField" => $SortField, "sortOrder" => $SortOrder);
    $Result = array("details" => $List, "pagination" => $Pagination, "status" => true);

    return $Result;
}

function PersonManagement($Values) {
    global $conn;

    $sql = "CALL Person_Management (?, ?, @flag_err)";
    $query = mysqli_prepare($conn, $sql);
    mysqli_stmt_bind_param($query, "ss", ...$Values);
    mysqli_stmt_execute($query);

    $flag_result = mysqli_query($conn, "SELECT @flag_err AS flag_err");
    $flag_err = mysqli_fetch_assoc($flag_result);

    $flag = $Values[1];
    if ($flag_err['flag_err'] == 0) {        
        if ($flag == 'create') {
            $rec = mysqli_stmt_affected_rows($query);
            $Code = ($rec == 1) ? "CREATE_PERSON_SUCESS" : "CREATE_PERSON_FAILED";
            $Msg = ($rec == 1) ? "Create person successfully" : "Create person failed";
            $Status = ($rec == 1) ?? false;
        } else if ($flag == 'update') {
            $Code = 'UPDATE_PERSON_SUCESS';
            $Msg = 'Update person successfully';
            $Status = true;
        } else if ($flag == 'delete') {
            $Code = 'DELETE_PERSON_SUCESS';
            $Msg = 'Delete person successfully';
            $Status = true;
        } else {
            $Code = 'PERSON_MANAGEMENT_FAILED';
            $Msg = 'Invalid parameters';
            $Status = false;
        }
    } else {
        $messages = [
            'create' => ['code' => 'CREATE_PERSON_FAILED', 'message' => 'Create person failed'],
            'update' => ['code' => 'UPDATE_PERSON_FAILED', 'message' => 'Update person failed'],
            'delete' => ['code' => 'DELETE_PERSON_FAILED', 'message' => 'Delete person failed'],
        ];

        $Code = isset($messages[$flag]) ? $messages[$flag]['code'] : 'PERSON_MANAGEMENT_FAILED';
        $Msg  = isset($messages[$flag]) ? $messages[$flag]['message'] : 'Invalid parameters';
        $Status = false;
    }

    $Result = array("code" => $Code, "message" => $Msg, "status" => $Status);
    return $Result;
}

function VerifyToken($token) {
    $curl = curl_init();
	curl_setopt($curl, CURLOPT_URL,				"https://aliza.k-lynx.com/api/v1/auth/introspect");
	curl_setopt($curl, CURLOPT_RETURNTRANSFER, 	true );
	curl_setopt($curl, CURLOPT_ENCODING, 		'');
	curl_setopt($curl, CURLOPT_MAXREDIRS, 		3);
	curl_setopt($curl, CURLOPT_TIMEOUT,        	5);
	curl_setopt($curl, CURLOPT_FOLLOWLOCATION,	true);
	curl_setopt($curl, CURLOPT_HTTP_VERSION, 	CURL_HTTP_VERSION_1_1);
	curl_setopt($curl, CURLOPT_CUSTOMREQUEST,   "GET");
    curl_setopt($curl, CURLOPT_HTTPHEADER, 		["Content-Type: application/json",
                                                                        "Authorization: Bearer ".$token]);
	
	$response = curl_exec($curl);
	$httpCode = curl_getinfo($curl, CURLINFO_HTTP_CODE);
	curl_close($curl);

    if ($response !== false && $httpCode == 200) {
        $Decode = json_decode($response, true);
        $Result = $Decode["detail"]["username"];
    } else {
        $Result = false;
    }

    return $Result;
}
?>
