<?php
header("Access-Control-Allow-Origin: *");
header("Content-Type: application/json; charset=UTF-8");
header("Access-Control-Allow-Methods: POST");
header("Access-Control-Max-Age: 3600");
header("Access-Control-Allow-Headers: Content-Type, Access-Control-Allow-Headers, Authorization, X-Requested-With");

include '../Condb.php';
include '../users.inc.php';
include '../Alert.php';
require_once("api_function.php");

$Token = (isset($_GET['token']) && trim($_GET['token'])) ? trim($_GET['token']) : '';
$Username = VerifyToken($Token);

if ($Username !== false) {
    if (isset($user["account"]) && $user["account"] !='') {
        header("Location: /WatchmanData/main.php");
    } else {
        if (user_login($Username, "", "new")) {
            echo '<h1>Please wait, Loading..</h1>';
            $_SESSION['message'] = "Log in successfully ";
            showSweetAlert2(
                "เงื่อนไขการใช้งานโปรแกรม",
                "ข้อมูลที่ใช้งานนี้ถือเป็นความลับของทางราชการ 
                โปรดใช้ข้อมูล เพื่อประโยชน์การป้องกันปราบปรามอาชญากรรมหรือรักษาความปลอดภัยในชีวิตและทรัพย์ของประชาชนเท่านั้น 
                ห้ามนำข้อมูลไปใช้ในส่วนที่ไม่เกี่ยวกับการปฏิบัติหน้าที่ราชการเด็ดขาด หากนำไปใช้แล้วเกิดความเสียหายหรือถูกร้องเรียน ผู้เข้าใช้ข้อมูลดังกล่าวจะต้องรับผิดชอบในการดำเนินการนั้น 
                และยินยอมให้โปรแกรมจัดการข้อมูลการใช้งานของผู้ใช้ทุกรายการ สำหรับตรวจสอบการเข้าถึงข้อมูลส่วนบุคคล",
                "info",
                "/WatchmanData/main.php" // Pass the redirection URL as the fourth argument
            );
            unset($_SESSION['message']); // Clear the session variable
            // No header redirect here, it will be handled by the SweetAlert2 callback
            exit;
        } else {
            $login_msg = "<h3>User หรือ Password ไม่ถูกต้อง<br>โปรดเข้าสู่ระบบอีกครั้ง หรือติดต่อผู้ดูแลระบบ</h3>";
        }
    }
} else {
    session_destroy();
    echo json_encode(["code" => "USERNAME_NOT_FOUND", 
                             "Username not found", 
                             "status" => false]);
}
return;
?>