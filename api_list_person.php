<?php
header("Access-Control-Allow-Origin: *");
header("Content-Type: application/json; charset=UTF-8");
header("Access-Control-Allow-Methods: GET");
header("Access-Control-Max-Age: 3600");
header("Access-Control-Allow-Headers: Content-Type, Access-Control-Allow-Headers, Authorization, X-Requested-With");

require_once("api_function.php");

if ($_SERVER['REQUEST_METHOD'] == "GET") {
    $Search = (isset($_GET["search"]) && strlen(trim($_GET["search"])) > 0) ? trim($_GET["search"]) : "";
    $Page = (isset($_GET["page"]) && $_GET["page"] > 0) ? intval($_GET["page"]) : 1;
    $PerPages = (isset($_GET["perPages"]) && $_GET["perPages"] > 0) ? intval($_GET["perPages"]) : 10;
    
    $Result = ListPersons($Search, $Page, $PerPages);
} else {
    $Result = array("code" => "LIST_PERSON_ERROR", 
                    "message" => "Incorrect method", 
                    "status" => false);
}

http_response_code(200);
echo json_encode($Result);
return;
?>