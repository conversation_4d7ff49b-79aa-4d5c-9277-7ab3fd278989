<?php
header("Access-Control-Allow-Origin: *");
header("Content-Type: application/json; charset=UTF-8");
header("Access-Control-Allow-Methods: GET");
header("Access-Control-Max-Age: 3600");
header("Access-Control-Allow-Headers: Content-Type, Access-Control-Allow-Headers, Authorization, X-Requested-With");

require_once("api_function.php");

if ($_SERVER['REQUEST_METHOD'] == "GET") {
    $FindBy = (isset($_GET["findby"]) && in_array(trim($_GET["findby"]), ["id", "idcard"])) ? trim($_GET["findby"]) : "id";
    $Id = (isset($_GET["id"]) && trim($_GET["id"]) !="") ? strval(trim($_GET["id"])) : "";

    if ($Id != "") {
        $InputVals = [$Id, $FindBy];
        $Result = GetPersonDetails($InputVals);
    } else {
        $Result = array("code" => "GET_PERSON_DETAILS_FAILED", 
                        "message" => "Invalid parameters", 
                        "status" => false);
    }
} else {
    $Result = array("code" => "GET_PERSON_DETAILS_ERROR", 
                    "message" => "Incorrect method", 
                    "status" => false);
}

http_response_code(200);
echo json_encode($Result);
return;
?>